# 🎬 YouTube API Download Plugin

## Overview
This plugin integrates with <PERSON><PERSON><PERSON><PERSON><PERSON>'s YouTube Download API to provide reliable YouTube video and audio downloads for your WhatsApp bot.

**Repository:** https://github.com/MatheusIshiyama/youtube-download-api

## 🚀 Features

### ✅ What's Included:
- **Video Downloads** - Download YouTube videos in MP4 format
- **Audio Downloads** - Download YouTube audio in MP3 format  
- **Video Information** - Get video details and thumbnails
- **Search Integration** - Search YouTube and select videos to download
- **Interactive Lists** - User-friendly selection interface
- **API Status Check** - Monitor API availability
- **Error Handling** - Comprehensive error management
- **Fallback Support** - Multiple API endpoints for reliability

### 🎯 Commands Available:

#### Video Downloads:
```
.ytapi <YouTube URL>                    # Download video directly
.ytapi <search term>                    # Search and select video
.ytapi https://youtu.be/dQw4w9WgXcQ    # Example with URL
.ytapi never gonna give you up          # Example with search
```

#### Audio Downloads:
```
.ytapi audio <YouTube URL>              # Download audio directly
.ytapi audio <search term>              # Search and select audio
.ytapi audio https://youtu.be/dQw4w9WgXcQ  # Example with URL
.ytapi audio despacito                  # Example with search
```

#### Video Information:
```
.ytapi info <YouTube URL>               # Get video details
.ytapi info https://youtu.be/dQw4w9WgXcQ   # Example
```

#### API Status:
```
.ytapi status                           # Check API availability
```

## 🛠️ Installation

### Method 1: Direct File Creation
1. The plugin file `youtube-api-dl.js` has been created in your `plugins/` directory
2. Restart your bot to load the plugin
3. Use `.ytapi` commands immediately

### Method 2: Via Plugin Command (Alternative)
If you want to install via GitHub Gist:
```
.plugin https://gist.github.com/your-username/your-gist-id
```

## 📋 API Details

### Base API URL:
```
https://youtube-download-api.matheusishiyama.repl.co
```

### Endpoints Used:
- `/info/?url=<youtube_url>` - Get video information
- `/mp3/?url=<youtube_url>` - Download MP3 audio
- `/mp4/?url=<youtube_url>` - Download MP4 video

### Response Format:
```json
{
  "title": "Video title",
  "thumbnail": "Video thumbnail url"
}
```

## 🔧 Configuration

### API Settings:
- **Timeout:** 60 seconds
- **Response Type:** Stream for downloads, JSON for info
- **User Agent:** Modern browser simulation
- **Fallback:** Multiple API endpoints supported

### Customization Options:
You can modify these settings in the plugin file:

```javascript
// API Configuration
const API_BASE_URL = 'https://youtube-download-api.matheusishiyama.repl.co'
const API_TIMEOUT = 60000 // 60 seconds

// Alternative API endpoints for fallback
const FALLBACK_APIS = [
  'https://youtube-download-api.matheusishiyama.repl.co',
  // Add more fallback APIs here if needed
]
```

## 🎮 Usage Examples

### Basic Video Download:
```
User: .ytapi https://www.youtube.com/watch?v=dQw4w9WgXcQ
Bot: 🔍 Starting video download...
Bot: ✅ Success! Sending video...
Bot: [Sends MP4 video file]
```

### Search and Download:
```
User: .ytapi funny cats
Bot: 🔍 Searching YouTube...
Bot: [Shows interactive list of search results]
User: [Selects option from list]
Bot: ✅ Success! Sending video...
Bot: [Sends selected video]
```

### Audio Download:
```
User: .ytapi audio https://www.youtube.com/watch?v=dQw4w9WgXcQ
Bot: 🎵 Starting audio download...
Bot: ✅ Success! Sending audio...
Bot: [Sends MP3 audio file]
```

### Video Information:
```
User: .ytapi info https://www.youtube.com/watch?v=dQw4w9WgXcQ
Bot: 📋 Getting video information...
Bot: [Sends thumbnail with video details]
```

## 🔍 Troubleshooting

### Common Issues:

#### API Not Responding:
```
User: .ytapi status
Bot: ❌ YouTube API Status
     🔴 Status: Offline
     ⚠️ Issue: API not responding
     Alternative: Try .ytdl command for local downloads
```

#### Video Not Found:
```
Bot: ❌ No results found for: [search term]
```

#### Download Failed:
```
Bot: ❌ Video download failed: [error message]
     💡 Try:
     • Different video
     • Audio download: .ytapi audio [url]
```

### Solutions:
1. **Check API Status:** Use `.ytapi status` command
2. **Try Alternative:** Use existing `.ytdl` command as fallback
3. **Different Format:** Try audio if video fails, or vice versa
4. **Wait and Retry:** API might be temporarily busy

## 🔄 Integration with Existing Plugins

### Compatibility:
- ✅ Works alongside existing `.ytdl` plugin
- ✅ Uses same search functionality (`yts`)
- ✅ Compatible with existing bot infrastructure
- ✅ No conflicts with other download plugins

### Differences from `.ytdl`:
| Feature | `.ytdl` | `.ytapi` |
|---------|---------|----------|
| Source | Local y2mate library | External API |
| Reliability | Depends on y2mate | Depends on external API |
| Speed | Variable | Generally faster |
| Quality Options | Multiple qualities | Standard quality |
| Offline Support | Yes | No |

## 📊 Performance

### Expected Performance:
- **Search:** 2-5 seconds
- **Video Info:** 3-8 seconds  
- **Audio Download:** 10-30 seconds
- **Video Download:** 15-60 seconds

### Factors Affecting Speed:
- Video length and quality
- API server load
- Network connection
- File size

## 🛡️ Error Handling

The plugin includes comprehensive error handling:

1. **Network Errors:** Timeout and connection issues
2. **API Errors:** Invalid responses and server errors
3. **Search Errors:** No results found
4. **Format Errors:** Invalid URLs or unsupported content
5. **File Errors:** Download and processing issues

## 🔮 Future Enhancements

### Planned Features:
- [ ] Quality selection for videos
- [ ] Playlist download support
- [ ] Download progress indicators
- [ ] Batch download capabilities
- [ ] Custom API endpoint configuration
- [ ] Download history tracking

### Contributing:
Feel free to suggest improvements or report issues!

## 📝 Credits

- **API Provider:** MatheusIshiyama
- **Original Repository:** https://github.com/MatheusIshiyama/youtube-download-api
- **Plugin Developer:** Augment Agent
- **Bot Framework:** Levanter

---

**Note:** This plugin requires an active internet connection and depends on the external API service. For offline functionality, use the existing `.ytdl` plugin instead.
