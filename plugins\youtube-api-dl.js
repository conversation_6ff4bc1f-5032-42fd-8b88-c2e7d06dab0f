/**
 * 🎬 YouTube Download API Plugin
 * 
 * Uses MatheusIshiyama's YouTube Download API for downloading videos and audio
 * API: https://github.com/MatheusIshiyama/youtube-download-api
 * 
 * Commands:
 * - .ytapi <url/search> - Download video with quality options
 * - .ytapi audio <url/search> - Download audio only
 * - .ytapi info <url> - Get video information
 */

const { bot, yts, getBuffer, isUrl, generateList } = require('../lib/')
const axios = require('axios')

// YouTube URL regex pattern
const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/

// API Configuration
const API_BASE_URL = 'https://youtube-download-api.matheusishiyama.repl.co'
const API_TIMEOUT = 60000 // 60 seconds

// Safe text formatting to prevent errors
const safeFormat = (text) => {
  if (!text) return 'Unknown'
  return String(text).replace(/[*_`~]/g, '').substring(0, 100)
}

// Validate YouTube URL
const isValidYouTubeUrl = (url) => {
  return ytIdRegex.test(url)
}

// Extract video ID from URL
const extractVideoId = (url) => {
  const match = ytIdRegex.exec(url)
  return match ? match[1] : null
}

// Main YouTube API Downloader Command
bot(
  {
    pattern: 'ytapi ?(.*)',
    desc: 'Download YouTube videos and audio using external API',
    type: 'download',
  },
  async (message, match) => {
    try {
      match = match || message.reply_message?.text
      if (!match) {
        return await message.send(`*🎬 YouTube API Downloader*

*📥 Video Downloads:*
• \`.ytapi <YouTube URL>\` - Download video
• \`.ytapi <search term>\` - Search and download video

*🎵 Audio Downloads:*
• \`.ytapi audio <YouTube URL>\` - Download audio only
• \`.ytapi audio <search term>\` - Search and download audio

*📋 Video Info:*
• \`.ytapi info <YouTube URL>\` - Get video information

*📝 Examples:*
\`.ytapi https://youtu.be/dQw4w9WgXcQ\`
\`.ytapi never gonna give you up\`
\`.ytapi audio despacito\`
\`.ytapi info https://youtu.be/dQw4w9WgXcQ\``)
      }

      // Check if it's an info request
      if (match.toLowerCase().startsWith('info ')) {
        match = match.substring(5).trim()
        return await handleVideoInfo(message, match)
      }

      // Check if it's an audio download request
      if (match.toLowerCase().startsWith('audio ')) {
        match = match.substring(6).trim()
        return await handleAudioDownload(message, match)
      }

      // Handle video download
      return await handleVideoDownload(message, match)
      
    } catch (error) {
      console.error('YouTube API Plugin Error:', error)
      return await message.send(`❌ *Plugin Error:* ${error.message}`)
    }
  }
)

// Handle video information requests
async function handleVideoInfo(message, input) {
  try {
    let videoUrl = input

    // If not a direct URL, search first
    if (!isValidYouTubeUrl(input)) {
      await message.send('🔍 *Searching YouTube...*')
      const searchResults = await yts(input, false, null, message.id)
      
      if (!searchResults || !searchResults.length) {
        return await message.send(`❌ *No results found for:* ${input}`)
      }

      const topResult = searchResults[0]
      videoUrl = `https://www.youtube.com/watch?v=${topResult.id}`
    }

    await message.send('📋 *Getting video information...*')
    
    const response = await axios.get(`${API_BASE_URL}/info/`, {
      params: { url: videoUrl },
      timeout: API_TIMEOUT,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (response.data && response.data.title) {
      const { title, thumbnail } = response.data
      const safeTitle = safeFormat(title)
      
      const infoMessage = `📋 *Video Information*

🎬 *Title:* ${safeTitle}
🔗 *URL:* ${videoUrl}
🖼️ *Thumbnail:* Available

*Available Downloads:*
• \`.ytapi ${videoUrl}\` - Download video
• \`.ytapi audio ${videoUrl}\` - Download audio`

      if (thumbnail && isUrl(thumbnail)) {
        return await message.sendFromUrl(thumbnail, {
          caption: infoMessage,
          quoted: message.data
        })
      } else {
        return await message.send(infoMessage)
      }
    } else {
      throw new Error('Invalid response from API')
    }
    
  } catch (error) {
    console.error('Video Info Error:', error)
    return await message.send(`❌ *Failed to get video info:* ${error.message}`)
  }
}

// Handle video downloads
async function handleVideoDownload(message, input) {
  try {
    let videoUrl = input

    // If not a direct URL, search first
    if (!isValidYouTubeUrl(input)) {
      await message.send('🔍 *Searching YouTube...*')
      const searchResults = await yts(input, false, null, message.id)
      
      if (!searchResults || !searchResults.length) {
        return await message.send(`❌ *No results found for:* ${input}`)
      }

      // Show search results for selection
      const msg = generateList(
        searchResults.slice(0, 8).map(({ title, id, duration, view, author }) => ({
          text: `🎬 ${safeFormat(title)}\n⏱️ ${duration || 'Unknown'} | 👁️ ${view || 'Unknown'}\n👤 ${safeFormat(author)}\n`,
          id: `ytapi https://www.youtube.com/watch?v=${id}`,
        })),
        `🔍 *Search Results for:* ${input}\n\nSelect video to download:`,
        message.jid,
        message.participant,
        message.id
      )
      return await message.send(msg.message, { quoted: message.data }, msg.type)
    }

    // Direct URL download
    await message.send('🎬 *Starting video download...*')
    
    const response = await axios.get(`${API_BASE_URL}/mp4/`, {
      params: { url: videoUrl },
      timeout: API_TIMEOUT,
      responseType: 'stream',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (response.status === 200) {
      await message.send('✅ *Success! Sending video...*')
      
      // Get video info for caption
      try {
        const infoResponse = await axios.get(`${API_BASE_URL}/info/`, {
          params: { url: videoUrl },
          timeout: 10000
        })
        
        const videoTitle = infoResponse.data?.title ? safeFormat(infoResponse.data.title) : 'YouTube Video'
        
        return await message.send(response.data, {
          quoted: message.data,
          caption: `🎬 *${videoTitle}*\n🔗 ${videoUrl}`,
          mimetype: 'video/mp4'
        }, 'video')
        
      } catch (infoError) {
        // Send without caption if info fails
        return await message.send(response.data, {
          quoted: message.data,
          mimetype: 'video/mp4'
        }, 'video')
      }
    } else {
      throw new Error(`API returned status ${response.status}`)
    }
    
  } catch (error) {
    console.error('Video Download Error:', error)
    return await message.send(`❌ *Video download failed:* ${error.message}\n\n💡 *Try:*\n• Different video\n• Audio download: \`.ytapi audio ${input}\``)
  }
}

// Handle audio downloads
async function handleAudioDownload(message, input) {
  try {
    let videoUrl = input

    // If not a direct URL, search first
    if (!isValidYouTubeUrl(input)) {
      await message.send('🔍 *Searching YouTube for audio...*')
      const searchResults = await yts(input, false, null, message.id)

      if (!searchResults || !searchResults.length) {
        return await message.send(`❌ *No results found for:* ${input}`)
      }

      // Show search results for selection
      const msg = generateList(
        searchResults.slice(0, 8).map(({ title, id, duration, view, author }) => ({
          text: `🎵 ${safeFormat(title)}\n⏱️ ${duration || 'Unknown'} | 👁️ ${view || 'Unknown'}\n👤 ${safeFormat(author)}\n`,
          id: `ytapi audio https://www.youtube.com/watch?v=${id}`,
        })),
        `🔍 *Audio Search Results for:* ${input}\n\nSelect audio to download:`,
        message.jid,
        message.participant,
        message.id
      )
      return await message.send(msg.message, { quoted: message.data }, msg.type)
    }

    // Direct URL audio download
    await message.send('🎵 *Starting audio download...*')

    const response = await axios.get(`${API_BASE_URL}/mp3/`, {
      params: { url: videoUrl },
      timeout: API_TIMEOUT,
      responseType: 'stream',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (response.status === 200) {
      await message.send('✅ *Success! Sending audio...*')

      // Get video info for filename and caption
      try {
        const infoResponse = await axios.get(`${API_BASE_URL}/info/`, {
          params: { url: videoUrl },
          timeout: 10000
        })

        const videoTitle = infoResponse.data?.title ? safeFormat(infoResponse.data.title) : 'YouTube Audio'

        return await message.send(response.data, {
          quoted: message.data,
          caption: `🎵 *${videoTitle}*\n🔗 ${videoUrl}`,
          mimetype: 'audio/mpeg',
          fileName: `${videoTitle}.mp3`
        }, 'audio')

      } catch (infoError) {
        // Send without caption if info fails
        return await message.send(response.data, {
          quoted: message.data,
          mimetype: 'audio/mpeg',
          fileName: 'YouTube_Audio.mp3'
        }, 'audio')
      }
    } else {
      throw new Error(`API returned status ${response.status}`)
    }

  } catch (error) {
    console.error('Audio Download Error:', error)
    return await message.send(`❌ *Audio download failed:* ${error.message}\n\n💡 *Try:*\n• Different video\n• Video download: \`.ytapi ${input}\``)
  }
}

// Alternative API endpoints for fallback
const FALLBACK_APIS = [
  'https://youtube-download-api.matheusishiyama.repl.co',
  // Add more fallback APIs here if needed
]

// Enhanced download function with fallback support
async function downloadWithFallback(endpoint, params, responseType = 'stream') {
  for (const apiUrl of FALLBACK_APIS) {
    try {
      const response = await axios.get(`${apiUrl}${endpoint}`, {
        params,
        timeout: API_TIMEOUT,
        responseType,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      if (response.status === 200) {
        return response
      }
    } catch (error) {
      console.log(`Fallback API ${apiUrl} failed:`, error.message)
      continue
    }
  }

  throw new Error('All API endpoints failed')
}

// Test API availability
async function testApiAvailability() {
  try {
    const response = await axios.get(API_BASE_URL, { timeout: 5000 })
    return response.status === 200
  } catch (error) {
    return false
  }
}

// Additional command for API status
bot(
  {
    pattern: 'ytapi status',
    desc: 'Check YouTube API status',
    type: 'download',
  },
  async (message) => {
    try {
      await message.send('🔍 *Checking API status...*')

      const isAvailable = await testApiAvailability()

      if (isAvailable) {
        return await message.send(`✅ *YouTube API Status*

🟢 *Status:* Online
🔗 *API URL:* ${API_BASE_URL}
⚡ *Response:* Fast

*Available Commands:*
• \`.ytapi <url/search>\` - Download video
• \`.ytapi audio <url/search>\` - Download audio
• \`.ytapi info <url>\` - Get video info`)
      } else {
        return await message.send(`❌ *YouTube API Status*

🔴 *Status:* Offline
🔗 *API URL:* ${API_BASE_URL}
⚠️ *Issue:* API not responding

*Alternative:* Try \`.ytdl\` command for local downloads`)
      }
    } catch (error) {
      return await message.send(`❌ *Error checking API status:* ${error.message}`)
    }
  }
)
