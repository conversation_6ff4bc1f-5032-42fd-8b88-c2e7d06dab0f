/**
 * 🎬 Video5 - API.Video Custom Downloader Plugin
 * 
 * Downloads videos from your api.video account using your API key
 * Command: .video5 - Lists and downloads videos from your api.video library
 * 
 * Features:
 * - List all videos in your account
 * - Download videos by selection
 * - Search videos by title
 * - Get video information
 */

const { bot, generateList, isUrl } = require('../lib/')
const axios = require('axios')

// API Configuration
const API_KEY = 'GndDicHo3f3te7KL2J44TfUlVMkLXhXuPILz424WJt0'
const API_BASE_URL = 'https://ws.api.video'
const API_TIMEOUT = 30000

// Safe text formatting
const safeFormat = (text) => {
  if (!text) return 'Unknown'
  return String(text).replace(/[*_`~]/g, '').substring(0, 80)
}

// Format duration from seconds to readable format
const formatDuration = (seconds) => {
  if (!seconds) return 'Unknown'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// Format file size
const formatFileSize = (bytes) => {
  if (!bytes) return 'Unknown'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

// Main Video5 Command
bot(
  {
    pattern: 'video5 ?(.*)',
    desc: 'List and download videos from your api.video account',
    type: 'download',
  },
  async (message, match) => {
    try {
      match = match?.trim()
      
      if (!match) {
        return await message.send(`*🎬 Video5 - API.Video Downloader*

*📋 Available Commands:*
• \`.video5 list\` - List all videos in your account
• \`.video5 search <title>\` - Search videos by title
• \`.video5 info <videoId>\` - Get detailed video information
• \`.video5 download <videoId>\` - Download specific video
• \`.video5 status\` - Check API connection status

*📝 Examples:*
\`.video5 list\`
\`.video5 search maths\`
\`.video5 info vi4blUQJFrYWbaG44NChkH27\`
\`.video5 download vi4blUQJFrYWbaG44NChkH27\`
\`.video5 status\``)
      }

      // Handle different subcommands
      if (match.toLowerCase() === 'list') {
        return await handleListVideos(message)
      }
      
      if (match.toLowerCase().startsWith('search ')) {
        const searchTerm = match.substring(7).trim()
        return await handleSearchVideos(message, searchTerm)
      }
      
      if (match.toLowerCase().startsWith('info ')) {
        const videoId = match.substring(5).trim()
        return await handleVideoInfo(message, videoId)
      }
      
      if (match.toLowerCase().startsWith('download ')) {
        const videoId = match.substring(9).trim()
        return await handleVideoDownload(message, videoId)
      }

      if (match.toLowerCase() === 'status') {
        return await handleApiStatus(message)
      }

      // If it looks like a video ID, try to download it
      if (match.startsWith('vi') && match.length > 10) {
        return await handleVideoDownload(message, match)
      }

      // Default to list if no specific command
      return await handleListVideos(message)
      
    } catch (error) {
      console.error('Video5 Plugin Error:', error)
      return await message.send(`❌ *Plugin Error:* ${error.message}`)
    }
  }
)

// Handle listing all videos
async function handleListVideos(message) {
  try {
    await message.send('📋 *Fetching your videos...*')
    
    const response = await axios.get(`${API_BASE_URL}/videos`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      params: {
        pageSize: 10,
        sortBy: 'publishedAt',
        sortOrder: 'desc'
      },
      timeout: API_TIMEOUT
    })

    if (!response.data || !response.data.data || response.data.data.length === 0) {
      return await message.send('📭 *No videos found in your account.*')
    }

    const videos = response.data.data
    const videoList = videos.map((video, index) => ({
      text: `🎬 ${safeFormat(video.title)}\n📅 ${new Date(video.publishedAt).toLocaleDateString()}\n🔗 ID: ${video.videoId}\n`,
      id: `video5 download ${video.videoId}`
    }))

    const msg = generateList(
      videoList,
      `📋 *Your Videos (${videos.length} found)*\n\nSelect a video to download:`,
      message.jid,
      message.participant,
      message.id
    )

    return await message.send(msg.message, { quoted: message.data }, msg.type)
    
  } catch (error) {
    console.error('List Videos Error:', error)
    return await message.send(`❌ *Failed to fetch videos:* ${error.response?.data?.detail || error.message}`)
  }
}

// Handle searching videos
async function handleSearchVideos(message, searchTerm) {
  try {
    if (!searchTerm) {
      return await message.send('❌ *Please provide a search term*\n\nExample: `.video5 search maths`')
    }

    await message.send(`🔍 *Searching for: "${searchTerm}"...*`)
    
    const response = await axios.get(`${API_BASE_URL}/videos`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      params: {
        title: searchTerm,
        pageSize: 10,
        sortBy: 'publishedAt',
        sortOrder: 'desc'
      },
      timeout: API_TIMEOUT
    })

    if (!response.data || !response.data.data || response.data.data.length === 0) {
      return await message.send(`📭 *No videos found matching: "${searchTerm}"*`)
    }

    const videos = response.data.data
    const videoList = videos.map((video, index) => ({
      text: `🎬 ${safeFormat(video.title)}\n📅 ${new Date(video.publishedAt).toLocaleDateString()}\n🔗 ID: ${video.videoId}\n`,
      id: `video5 download ${video.videoId}`
    }))

    const msg = generateList(
      videoList,
      `🔍 *Search Results for "${searchTerm}" (${videos.length} found)*\n\nSelect a video to download:`,
      message.jid,
      message.participant,
      message.id
    )

    return await message.send(msg.message, { quoted: message.data }, msg.type)
    
  } catch (error) {
    console.error('Search Videos Error:', error)
    return await message.send(`❌ *Search failed:* ${error.response?.data?.detail || error.message}`)
  }
}

// Handle video information
async function handleVideoInfo(message, videoId) {
  try {
    if (!videoId) {
      return await message.send('❌ *Please provide a video ID*\n\nExample: `.video5 info vi4blUQJFrYWbaG44NChkH27`')
    }

    await message.send('📋 *Getting video information...*')
    
    const response = await axios.get(`${API_BASE_URL}/videos/${videoId}`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    })

    const video = response.data
    const safeTitle = safeFormat(video.title)
    const safeDescription = safeFormat(video.description || 'No description')
    
    const infoMessage = `📋 *Video Information*

🎬 *Title:* ${safeTitle}
📝 *Description:* ${safeDescription}
🆔 *Video ID:* ${video.videoId}
📅 *Published:* ${new Date(video.publishedAt).toLocaleString()}
🔒 *Public:* ${video.public ? 'Yes' : 'No'}
🎭 *Panoramic:* ${video.panoramic ? 'Yes' : 'No'}
📱 *MP4 Support:* ${video.mp4Support ? 'Yes' : 'No'}
🏷️ *Tags:* ${video.tags?.join(', ') || 'None'}

*🔗 Available Downloads:*
• \`.video5 download ${video.videoId}\` - Download video`

    if (video.assets?.thumbnail && isUrl(video.assets.thumbnail)) {
      return await message.sendFromUrl(video.assets.thumbnail, {
        caption: infoMessage,
        quoted: message.data
      })
    } else {
      return await message.send(infoMessage)
    }
    
  } catch (error) {
    console.error('Video Info Error:', error)
    if (error.response?.status === 404) {
      return await message.send(`❌ *Video not found:* ${videoId}`)
    }
    return await message.send(`❌ *Failed to get video info:* ${error.response?.data?.detail || error.message}`)
  }
}

// Handle video download
async function handleVideoDownload(message, videoId) {
  try {
    if (!videoId) {
      return await message.send('❌ *Please provide a video ID*\n\nExample: `.video5 download vi4blUQJFrYWbaG44NChkH27`')
    }

    await message.send('🎬 *Preparing video download...*')
    
    // First get video info
    const videoResponse = await axios.get(`${API_BASE_URL}/videos/${videoId}`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    })

    const video = videoResponse.data
    
    if (!video.assets?.mp4) {
      return await message.send('❌ *Video MP4 not available for download*')
    }

    const safeTitle = safeFormat(video.title)
    await message.send(`📥 *Downloading: ${safeTitle}*\n\n⏳ *Please wait...*`)

    // Download the video
    return await message.sendFromUrl(video.assets.mp4, {
      quoted: message.data,
      caption: `🎬 *${safeTitle}*\n🆔 ${video.videoId}\n📅 ${new Date(video.publishedAt).toLocaleDateString()}`,
      mimetype: 'video/mp4'
    })
    
  } catch (error) {
    console.error('Video Download Error:', error)
    if (error.response?.status === 404) {
      return await message.send(`❌ *Video not found:* ${videoId}`)
    }
    return await message.send(`❌ *Download failed:* ${error.response?.data?.detail || error.message}`)
  }
}

// Handle API status check
async function handleApiStatus(message) {
  try {
    await message.send('🔍 *Checking API status...*')

    const startTime = Date.now()
    const response = await axios.get(`${API_BASE_URL}/videos`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      params: {
        pageSize: 1
      },
      timeout: 10000
    })

    const responseTime = Date.now() - startTime
    const totalVideos = response.data.pagination?.itemsTotal || 0

    return await message.send(`✅ *API.Video Status*

🟢 *Status:* Online
🔗 *API URL:* ${API_BASE_URL}
⚡ *Response Time:* ${responseTime}ms
🎬 *Total Videos:* ${totalVideos}
🔑 *API Key:* ${API_KEY.substring(0, 8)}...

*Available Commands:*
• \`.video5 list\` - List your videos
• \`.video5 search <term>\` - Search videos
• \`.video5 download <id>\` - Download video`)

  } catch (error) {
    console.error('API Status Error:', error)

    let errorMessage = '❌ *API.Video Status*\n\n🔴 *Status:* Offline\n'

    if (error.response?.status === 401) {
      errorMessage += '🔑 *Issue:* Invalid API key\n'
    } else if (error.response?.status === 403) {
      errorMessage += '🚫 *Issue:* Access forbidden\n'
    } else if (error.code === 'ENOTFOUND') {
      errorMessage += '🌐 *Issue:* Network connection failed\n'
    } else {
      errorMessage += `⚠️ *Issue:* ${error.message}\n`
    }

    errorMessage += '\n*Troubleshooting:*\n• Check your internet connection\n• Verify API key is correct\n• Try again in a few minutes'

    return await message.send(errorMessage)
  }
}

// Additional utility function to get video status
async function getVideoStatus(videoId) {
  try {
    const response = await axios.get(`${API_BASE_URL}/videos/${videoId}/status`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    })

    return response.data
  } catch (error) {
    console.error('Video Status Error:', error)
    return null
  }
}
