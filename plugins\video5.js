/**
 * 🎬 Video5 - Enhanced Video Downloader Plugin
 *
 * Downloads videos from ANY source using API.Video processing
 * Command: .video5 - Universal video downloader for YouTube, TikTok, Instagram, etc.
 *
 * Features:
 * - Download from YouTube, TikTok, Instagram, Twitter, etc.
 * - List and manage your API.Video library
 * - Search videos by title
 * - Get video information
 * - URL import and processing via API.Video
 */

const { bot, generateList, isUrl, yts, y2mate } = require('../lib/')
const axios = require('axios')

// API Configuration
const API_KEY = 'GndDicHo3f3te7KL2J44TfUlVMkLXhXuPILz424WJt0'
const API_BASE_URL = 'https://ws.api.video'
const API_TIMEOUT = 30000

// URL Detection Patterns
const URL_PATTERNS = {
  youtube: /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/,
  tiktok: /(?:tiktok\.com\/@[^\/]+\/video\/|vm\.tiktok\.com\/|vt\.tiktok\.com\/)([0-9]+)/,
  instagram: /(?:instagram\.com\/(?:p|reel|tv)\/|instagr\.am\/p\/)([A-Za-z0-9_-]+)/,
  twitter: /(?:twitter\.com|x\.com)\/[^\/]+\/status\/([0-9]+)/,
  facebook: /(?:facebook\.com|fb\.watch)\/.*\/videos?\/([0-9]+)/,
  generic: /^https?:\/\/.+/
}

// Safe text formatting
const safeFormat = (text) => {
  if (!text) return 'Unknown'
  return String(text).replace(/[*_`~]/g, '').substring(0, 80)
}

// Format duration from seconds to readable format
const formatDuration = (seconds) => {
  if (!seconds) return 'Unknown'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// Format file size
const formatFileSize = (bytes) => {
  if (!bytes) return 'Unknown'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

// Detect URL type and platform
const detectUrlType = (url) => {
  if (!url || !isUrl(url)) return null

  for (const [platform, pattern] of Object.entries(URL_PATTERNS)) {
    if (pattern.test(url)) {
      return platform
    }
  }
  return null
}

// Main Video5 Command
bot(
  {
    pattern: 'video5 ?(.*)',
    desc: 'List and download videos from your api.video account',
    type: 'download',
  },
  async (message, match) => {
    try {
      match = match?.trim()
      
      if (!match) {
        return await message.send(`*🎬 Video5 - Universal Video Downloader*

*🌐 Download from ANY Platform:*
• \`.video5 <YouTube URL>\` - Download from YouTube
• \`.video5 <TikTok URL>\` - Download from TikTok
• \`.video5 <Instagram URL>\` - Download from Instagram
• \`.video5 <Twitter URL>\` - Download from Twitter/X
• \`.video5 <Any Video URL>\` - Download from any platform

*📋 Manage Your Library:*
• \`.video5 list\` - List videos in your account
• \`.video5 search <title>\` - Search your videos
• \`.video5 info <videoId>\` - Get video information
• \`.video5 status\` - Check API status

*📝 Examples:*
\`.video5 https://youtu.be/dQw4w9WgXcQ\`
\`.video5 https://tiktok.com/@user/video/123\`
\`.video5 list\`
\`.video5 search maths\``)
      }

      // Handle different subcommands
      if (match.toLowerCase() === 'list') {
        return await handleListVideos(message)
      }
      
      if (match.toLowerCase().startsWith('search ')) {
        const searchTerm = match.substring(7).trim()
        return await handleSearchVideos(message, searchTerm)
      }
      
      if (match.toLowerCase().startsWith('info ')) {
        const videoId = match.substring(5).trim()
        return await handleVideoInfo(message, videoId)
      }
      
      if (match.toLowerCase().startsWith('download ')) {
        const videoId = match.substring(9).trim()
        return await handleVideoDownload(message, videoId)
      }

      if (match.toLowerCase() === 'status') {
        return await handleApiStatus(message)
      }

      // Check if it's a URL for external download
      if (isUrl(match)) {
        const urlType = detectUrlType(match)
        if (urlType) {
          return await handleExternalDownload(message, match, urlType)
        }
      }

      // If it looks like a video ID, try to download it
      if (match.startsWith('vi') && match.length > 10) {
        return await handleVideoDownload(message, match)
      }

      // Default to list if no specific command
      return await handleListVideos(message)
      
    } catch (error) {
      console.error('Video5 Plugin Error:', error)
      return await message.send(`❌ *Plugin Error:* ${error.message}`)
    }
  }
)

// Handle listing all videos
async function handleListVideos(message) {
  try {
    await message.send('📋 *Fetching your videos...*')
    
    const response = await axios.get(`${API_BASE_URL}/videos`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      params: {
        pageSize: 10,
        sortBy: 'publishedAt',
        sortOrder: 'desc'
      },
      timeout: API_TIMEOUT
    })

    if (!response.data || !response.data.data || response.data.data.length === 0) {
      return await message.send('📭 *No videos found in your account.*')
    }

    const videos = response.data.data
    const videoList = videos.map((video, index) => ({
      text: `🎬 ${safeFormat(video.title)}\n📅 ${new Date(video.publishedAt).toLocaleDateString()}\n🔗 ID: ${video.videoId}\n`,
      id: `video5 download ${video.videoId}`
    }))

    const msg = generateList(
      videoList,
      `📋 *Your Videos (${videos.length} found)*\n\nSelect a video to download:`,
      message.jid,
      message.participant,
      message.id
    )

    return await message.send(msg.message, { quoted: message.data }, msg.type)
    
  } catch (error) {
    console.error('List Videos Error:', error)
    return await message.send(`❌ *Failed to fetch videos:* ${error.response?.data?.detail || error.message}`)
  }
}

// Handle searching videos
async function handleSearchVideos(message, searchTerm) {
  try {
    if (!searchTerm) {
      return await message.send('❌ *Please provide a search term*\n\nExample: `.video5 search maths`')
    }

    await message.send(`🔍 *Searching for: "${searchTerm}"...*`)
    
    const response = await axios.get(`${API_BASE_URL}/videos`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      params: {
        title: searchTerm,
        pageSize: 10,
        sortBy: 'publishedAt',
        sortOrder: 'desc'
      },
      timeout: API_TIMEOUT
    })

    if (!response.data || !response.data.data || response.data.data.length === 0) {
      return await message.send(`📭 *No videos found matching: "${searchTerm}"*`)
    }

    const videos = response.data.data
    const videoList = videos.map((video, index) => ({
      text: `🎬 ${safeFormat(video.title)}\n📅 ${new Date(video.publishedAt).toLocaleDateString()}\n🔗 ID: ${video.videoId}\n`,
      id: `video5 download ${video.videoId}`
    }))

    const msg = generateList(
      videoList,
      `🔍 *Search Results for "${searchTerm}" (${videos.length} found)*\n\nSelect a video to download:`,
      message.jid,
      message.participant,
      message.id
    )

    return await message.send(msg.message, { quoted: message.data }, msg.type)
    
  } catch (error) {
    console.error('Search Videos Error:', error)
    return await message.send(`❌ *Search failed:* ${error.response?.data?.detail || error.message}`)
  }
}

// Handle video information
async function handleVideoInfo(message, videoId) {
  try {
    if (!videoId) {
      return await message.send('❌ *Please provide a video ID*\n\nExample: `.video5 info vi4blUQJFrYWbaG44NChkH27`')
    }

    await message.send('📋 *Getting video information...*')
    
    const response = await axios.get(`${API_BASE_URL}/videos/${videoId}`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    })

    const video = response.data
    const safeTitle = safeFormat(video.title)
    const safeDescription = safeFormat(video.description || 'No description')
    
    const infoMessage = `📋 *Video Information*

🎬 *Title:* ${safeTitle}
📝 *Description:* ${safeDescription}
🆔 *Video ID:* ${video.videoId}
📅 *Published:* ${new Date(video.publishedAt).toLocaleString()}
🔒 *Public:* ${video.public ? 'Yes' : 'No'}
🎭 *Panoramic:* ${video.panoramic ? 'Yes' : 'No'}
📱 *MP4 Support:* ${video.mp4Support ? 'Yes' : 'No'}
🏷️ *Tags:* ${video.tags?.join(', ') || 'None'}

*🔗 Available Downloads:*
• \`.video5 download ${video.videoId}\` - Download video`

    if (video.assets?.thumbnail && isUrl(video.assets.thumbnail)) {
      return await message.sendFromUrl(video.assets.thumbnail, {
        caption: infoMessage,
        quoted: message.data
      })
    } else {
      return await message.send(infoMessage)
    }
    
  } catch (error) {
    console.error('Video Info Error:', error)
    if (error.response?.status === 404) {
      return await message.send(`❌ *Video not found:* ${videoId}`)
    }
    return await message.send(`❌ *Failed to get video info:* ${error.response?.data?.detail || error.message}`)
  }
}

// Handle video download
async function handleVideoDownload(message, videoId) {
  try {
    if (!videoId) {
      return await message.send('❌ *Please provide a video ID*\n\nExample: `.video5 download vi4blUQJFrYWbaG44NChkH27`')
    }

    await message.send('🎬 *Preparing video download...*')
    
    // First get video info
    const videoResponse = await axios.get(`${API_BASE_URL}/videos/${videoId}`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    })

    const video = videoResponse.data
    
    if (!video.assets?.mp4) {
      return await message.send('❌ *Video MP4 not available for download*')
    }

    const safeTitle = safeFormat(video.title)
    await message.send(`📥 *Downloading: ${safeTitle}*\n\n⏳ *Please wait...*`)

    // Download the video
    return await message.sendFromUrl(video.assets.mp4, {
      quoted: message.data,
      caption: `🎬 *${safeTitle}*\n🆔 ${video.videoId}\n📅 ${new Date(video.publishedAt).toLocaleDateString()}`,
      mimetype: 'video/mp4'
    })
    
  } catch (error) {
    console.error('Video Download Error:', error)
    if (error.response?.status === 404) {
      return await message.send(`❌ *Video not found:* ${videoId}`)
    }
    return await message.send(`❌ *Download failed:* ${error.response?.data?.detail || error.message}`)
  }
}

// Handle API status check
async function handleApiStatus(message) {
  try {
    await message.send('🔍 *Checking API status...*')

    const startTime = Date.now()
    const response = await axios.get(`${API_BASE_URL}/videos`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      params: {
        pageSize: 1
      },
      timeout: 10000
    })

    const responseTime = Date.now() - startTime
    const totalVideos = response.data.pagination?.itemsTotal || 0

    return await message.send(`✅ *API.Video Status*

🟢 *Status:* Online
🔗 *API URL:* ${API_BASE_URL}
⚡ *Response Time:* ${responseTime}ms
🎬 *Total Videos:* ${totalVideos}
🔑 *API Key:* ${API_KEY.substring(0, 8)}...

*Available Commands:*
• \`.video5 list\` - List your videos
• \`.video5 search <term>\` - Search videos
• \`.video5 download <id>\` - Download video`)

  } catch (error) {
    console.error('API Status Error:', error)

    let errorMessage = '❌ *API.Video Status*\n\n🔴 *Status:* Offline\n'

    if (error.response?.status === 401) {
      errorMessage += '🔑 *Issue:* Invalid API key\n'
    } else if (error.response?.status === 403) {
      errorMessage += '🚫 *Issue:* Access forbidden\n'
    } else if (error.code === 'ENOTFOUND') {
      errorMessage += '🌐 *Issue:* Network connection failed\n'
    } else {
      errorMessage += `⚠️ *Issue:* ${error.message}\n`
    }

    errorMessage += '\n*Troubleshooting:*\n• Check your internet connection\n• Verify API key is correct\n• Try again in a few minutes'

    return await message.send(errorMessage)
  }
}

// Handle external URL downloads
async function handleExternalDownload(message, url, urlType) {
  try {
    const platformEmojis = {
      youtube: '📺',
      tiktok: '🎵',
      instagram: '📸',
      twitter: '🐦',
      facebook: '📘',
      generic: '🌐'
    }

    const emoji = platformEmojis[urlType] || '🌐'
    const platform = urlType.charAt(0).toUpperCase() + urlType.slice(1)

    await message.send(`${emoji} *Downloading from ${platform}...*\n\n🔗 ${url}\n⏳ *Please wait...*`)

    // Method 1: Try using y2mate for YouTube
    if (urlType === 'youtube') {
      try {
        const videoId = url.match(URL_PATTERNS.youtube)?.[1]
        if (videoId) {
          const result = await y2mate.get(videoId, 'video')
          if (isUrl(result)) {
            await message.send('✅ *Download successful! Sending video...*')
            return await message.sendFromUrl(result, {
              quoted: message.data,
              caption: `${emoji} *Downloaded from ${platform}*\n🔗 ${url}`,
              mimetype: 'video/mp4'
            })
          }
        }
      } catch (y2mateError) {
        console.log('Y2mate failed, trying API.Video method:', y2mateError.message)
      }
    }

    // Method 2: Try API.Video URL import (for all platforms)
    try {
      await message.send('🔄 *Trying alternative method...*')

      const uploadResponse = await axios.post(`${API_BASE_URL}/videos`, {
        title: `Downloaded from ${platform} - ${new Date().toISOString()}`,
        source: {
          type: 'url',
          url: url
        },
        public: false
      }, {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT
      })

      if (uploadResponse.data && uploadResponse.data.videoId) {
        const videoId = uploadResponse.data.videoId

        // Wait for processing and then download
        await message.send('⚙️ *Processing video... This may take a moment...*')

        // Poll for video readiness
        let attempts = 0
        const maxAttempts = 30 // 5 minutes max

        while (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 10000)) // Wait 10 seconds

          try {
            const videoResponse = await axios.get(`${API_BASE_URL}/videos/${videoId}`, {
              headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
              },
              timeout: 10000
            })

            if (videoResponse.data.assets?.mp4) {
              await message.send('✅ *Processing complete! Sending video...*')
              return await message.sendFromUrl(videoResponse.data.assets.mp4, {
                quoted: message.data,
                caption: `${emoji} *Downloaded from ${platform}*\n🔗 ${url}\n🆔 ${videoId}`,
                mimetype: 'video/mp4'
              })
            }
          } catch (pollError) {
            console.log('Polling attempt failed:', pollError.message)
          }

          attempts++
          if (attempts % 3 === 0) {
            await message.send(`⏳ *Still processing... (${attempts * 10}s)*`)
          }
        }

        throw new Error('Video processing timeout')
      }
    } catch (apiError) {
      console.error('API.Video import failed:', apiError)
    }

    // Method 3: Fallback to search and download for YouTube
    if (urlType === 'youtube') {
      try {
        await message.send('🔄 *Trying search method...*')

        // Extract video title from URL or use generic search
        const searchResults = await yts(url, false, null, message.id)

        if (searchResults && searchResults.length > 0) {
          const topResult = searchResults[0]
          const result = await y2mate.get(topResult.id, 'video')

          if (isUrl(result)) {
            await message.send('✅ *Fallback method successful! Sending video...*')
            return await message.sendFromUrl(result, {
              quoted: message.data,
              caption: `${emoji} *Downloaded from ${platform}*\n🎬 ${topResult.title}\n🔗 ${url}`,
              mimetype: 'video/mp4'
            })
          }
        }
      } catch (fallbackError) {
        console.error('Fallback method failed:', fallbackError)
      }
    }

    // If all methods fail
    throw new Error(`Unable to download from ${platform}. The video might be private, restricted, or the platform is not supported.`)

  } catch (error) {
    console.error('External Download Error:', error)
    return await message.send(`❌ *Download failed:* ${error.message}\n\n💡 *Try:*\n• Different video URL\n• Using \`.ytdl ${url}\` for YouTube\n• Checking if video is public`)
  }
}

// Additional utility function to get video status
async function getVideoStatus(videoId) {
  try {
    const response = await axios.get(`${API_BASE_URL}/videos/${videoId}/status`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    })

    return response.data
  } catch (error) {
    console.error('Video Status Error:', error)
    return null
  }
}
