/**
 * 🎵 Video Download API YouTube Downloader Plugin
 *
 * This plugin uses video-download-api.com service for YouTube downloads
 * Website: https://video-download-api.com/admin
 * API Key: 529f2b6e85dae86e40bf778f406140c7e7130d57
 * 
 * Commands: .yt1z <url/search> - Download using video-download-api.com
 *
 * Usage:
 * .yt1z https://youtu.be/dQw4w9WgXcQ
 * .yt1z never gonna give you up auto
 * .yt1z despacito
 */

const { bot, yts, isUrl } = require('../lib/')
const axios = require('axios')

// YouTube URL regex
const ytIdRegex = /(?:http(?:s|):\/\/|)(?:(?:www\.|)youtube(?:\-nocookie|)\.com\/(?:watch\?.*(?:|\&)v=|embed|shorts\/|v\/)|youtu\.be\/)([-_0-9A-Za-z]{11})/

// API Configuration
const API_CONFIG = {
  baseUrl: 'https://video-download-api.com/',
  apiKey: '529f2b6e85dae86e40bf778f406140c7e7130d57'
}

// Safe text formatting
const safeFormat = (text) => {
  if (!text) return 'Unknown'
  return String(text).replace(/[*_`~]/g, '').substring(0, 100)
}

// Video Download API class
class VideoDownloadAPI {
  constructor() {
    this.baseUrl = API_CONFIG.baseUrl
    this.apiKey = API_CONFIG.apiKey
  }

  // Get download link from video-download-api.com
  async getDownloadLink(videoUrl, format = 'mp3') {
    try {
      console.log('VideoDownloadAPI: Starting download process...')
      
      // Method 1: Try direct API call with JSON
      try {
        const response = await axios.post(`${this.baseUrl}/api/download`, {
          url: videoUrl,
          format: format,
          quality: format === 'mp3' ? '128' : '720'
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
            'X-API-Key': this.apiKey,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json'
          },
          timeout: 30000
        })

        if (response.data && response.data.downloadUrl) {
          console.log('VideoDownloadAPI: Direct API success')
          return response.data.downloadUrl
        }
      } catch (apiError) {
        console.log('VideoDownloadAPI: Direct API failed, trying alternatives...')
      }

      // Method 2: Try GET request with parameters
      try {
        const altResponse = await axios.get(`${this.baseUrl}/download`, {
          params: {
            url: videoUrl,
            format: format,
            api_key: this.apiKey
          },
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json'
          },
          timeout: 30000
        })

        if (altResponse.data && altResponse.data.download_url) {
          console.log('VideoDownloadAPI: Alternative endpoint success')
          return altResponse.data.download_url
        }
      } catch (getError) {
        console.log('VideoDownloadAPI: GET method failed, trying form submission...')
      }

      // Method 3: Try form-based submission
      try {
        const formData = new URLSearchParams()
        formData.append('url', videoUrl)
        formData.append('format', format)
        formData.append('api_key', this.apiKey)

        const formResponse = await axios.post(`${this.baseUrl}/convert`, formData, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json'
          },
          timeout: 30000
        })

        if (formResponse.data) {
          // Look for download URL in various response formats
          const downloadUrl = formResponse.data.downloadUrl || 
                             formResponse.data.download_url || 
                             formResponse.data.url ||
                             formResponse.data.link ||
                             formResponse.data.file_url

          if (downloadUrl && isUrl(downloadUrl)) {
            console.log('VideoDownloadAPI: Form submission success')
            return downloadUrl
          }
        }
      } catch (formError) {
        console.log('VideoDownloadAPI: Form submission failed')
      }

      // Method 4: Try direct website scraping as fallback
      try {
        const webResponse = await axios.get(this.baseUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          },
          timeout: 15000
        })

        // Submit to the website form
        const submitData = new URLSearchParams()
        submitData.append('url', videoUrl)
        submitData.append('api_key', this.apiKey)

        const submitResponse = await axios.post(this.baseUrl, submitData, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': this.baseUrl
          },
          timeout: 30000
        })

        // Parse HTML response for download links
        const html = submitResponse.data
        const downloadPatterns = [
          /href="([^"]*\.mp3[^"]*)"[^>]*>.*?(?:download|Download)/i,
          /href="([^"]*download[^"]*\.mp3[^"]*)"[^>]*>/i,
          /"downloadUrl":"([^"]*\.mp3[^"]*)"/,
          /"download_url":"([^"]*\.mp3[^"]*)"/,
          /data-url="([^"]*\.mp3[^"]*)"/i
        ]

        for (const pattern of downloadPatterns) {
          const match = html.match(pattern)
          if (match && match[1]) {
            let downloadUrl = match[1]
            
            if (!downloadUrl.startsWith('http')) {
              downloadUrl = downloadUrl.startsWith('/') ? 
                `https://video-download-api.com${downloadUrl}` : 
                `${this.baseUrl}/${downloadUrl}`
            }
            
            console.log('VideoDownloadAPI: Website scraping success')
            return downloadUrl
          }
        }
      } catch (webError) {
        console.log('VideoDownloadAPI: Website scraping failed')
      }

      throw new Error('All download methods failed - API may be down or changed')

    } catch (error) {
      console.error('VideoDownloadAPI Error:', error.message)
      
      if (error.response) {
        console.error('API Response Status:', error.response.status)
        console.error('API Response Data:', error.response.data)
      }
      
      throw new Error(`Video Download API failed: ${error.message}`)
    }
  }

  // Test API connectivity
  async testConnection() {
    try {
      const response = await axios.get(`${this.baseUrl}/status`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'X-API-Key': this.apiKey
        },
        timeout: 10000
      })
      
      return response.status === 200
    } catch (error) {
      // Try alternative status check
      try {
        const altResponse = await axios.get(this.baseUrl, {
          timeout: 10000
        })
        return altResponse.status === 200
      } catch (altError) {
        console.log('API connection test failed:', error.message)
        return false
      }
    }
  }
}

// Main plugin command
bot(
  {
    pattern: 'yt1z ?(.*)',
    fromMe: false,
    desc: 'Download YouTube audio using video-download-api.com service',
    type: 'download',
  },
  async (message, match) => {
    try {
      match = match || message.reply_message?.text
      if (!match) {
        return await message.send(`*🎵 Video Download API YouTube Downloader*

*📝 Usage:*
• \`.yt1z <YouTube URL>\` - Download audio from URL
• \`.yt1z <search term>\` - Search and download audio
• \`.yt1z <search term> auto\` - Auto download first result

*📝 Examples:*
\`.yt1z https://youtu.be/dQw4w9WgXcQ\`
\`.yt1z never gonna give you up auto\`
\`.yt1z despacito\`

*🌐 Powered by video-download-api.com*`)
      }

      const downloader = new VideoDownloadAPI()
      const vid = ytIdRegex.exec(match)
      const isAutoDownload = match.toLowerCase().includes(' auto')
      const searchTerm = match.replace(/ auto$/i, '').trim()

      let videoUrl, videoInfo

      // If not a direct URL, search first
      if (!vid) {
        await message.send('🔍 *Searching YouTube...*')
        const result = await yts(searchTerm, false, null, message.id)
        
        if (!result || !result.length) {
          return await message.send(`❌ *No results found for:* ${searchTerm}`)
        }

        if (isAutoDownload) {
          // Auto download first result
          const topResult = result[0]
          videoUrl = `https://www.youtube.com/watch?v=${topResult.id}`
          videoInfo = topResult
          await message.send(`🎵 *Auto-downloading via Video Download API:* ${safeFormat(topResult.title)}`)
        } else {
          // Show search results for selection
          const { generateList } = require('../lib/')
          const msg = generateList(
            result.slice(0, 8).map(({ title, id, duration, view, author }) => ({
              text: `🎵 ${safeFormat(title)}\n⏱️ ${duration || 'Unknown'} | 👁️ ${view || 'Unknown'}\n👤 ${safeFormat(author)}\n`,
              id: `yt1z https://www.youtube.com/watch?v=${id}`,
            })),
            `🔍 *Video Download API Search Results for:* ${searchTerm}\n\nSelect audio to download:`,
            message.jid,
            message.participant,
            message.id
          )
          return await message.send(msg.message, { quoted: message.data }, msg.type)
        }
      } else {
        // Direct URL
        const videoId = vid[1]
        videoUrl = `https://www.youtube.com/watch?v=${videoId}`
        
        await message.send('📋 *Getting video info...*')
        const [info] = await yts(videoId, true, null, message.id)
        videoInfo = info
      }

      // Download using Video Download API
      const safeTitle = safeFormat(videoInfo.title)
      const safeAuthor = safeFormat(videoInfo.author)
      const safeDuration = videoInfo.duration || 'Unknown'

      await message.send(`🎵 *${safeTitle}*\n👤 ${safeAuthor}\n⏱️ ${safeDuration}\n\n🌐 *Processing via Video Download API...*`)

      const downloadUrl = await downloader.getDownloadLink(videoUrl, 'mp3')
      
      if (!isUrl(downloadUrl)) {
        throw new Error('Invalid download URL received from Video Download API')
      }

      await message.send('✅ *Video Download API processing complete! Sending audio...*')
      
      return await message.sendFromUrl(downloadUrl, {
        quoted: message.data,
        mimetype: 'audio/mpeg',
        fileName: `${safeTitle}.mp3`
      })
      
    } catch (error) {
      console.error('Video Download API Plugin Error:', error)
      return await message.send(`❌ *Video Download API failed:* ${error.message}\n\n💡 *Try:*\n• Different video\n• Use regular \`.play\` command\n• Check if video-download-api.com is accessible`)
    }
  }
)
