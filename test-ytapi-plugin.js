/**
 * Test script for YouTube API Plugin
 * 
 * This script tests the basic functionality of the YouTube API plugin
 * without actually running the bot.
 */

const axios = require('axios')

// Test configuration
const API_BASE_URL = 'https://youtube-download-api.matheusishiyama.repl.co'
const TEST_VIDEO_URL = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
const API_TIMEOUT = 10000

console.log('🧪 Testing YouTube API Plugin...\n')

// Test 1: API Availability
async function testApiAvailability() {
  console.log('🔍 Test 1: Checking API availability...')
  try {
    const response = await axios.get(API_BASE_URL, { 
      timeout: 5000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    
    if (response.status === 200) {
      console.log('✅ API is available and responding')
      return true
    } else {
      console.log(`❌ API returned status: ${response.status}`)
      return false
    }
  } catch (error) {
    console.log(`❌ API not available: ${error.message}`)
    return false
  }
}

// Test 2: Video Info Endpoint
async function testVideoInfo() {
  console.log('\n🔍 Test 2: Testing video info endpoint...')
  try {
    const response = await axios.get(`${API_BASE_URL}/info/`, {
      params: { url: TEST_VIDEO_URL },
      timeout: API_TIMEOUT,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (response.data && response.data.title) {
      console.log('✅ Video info endpoint working')
      console.log(`   Title: ${response.data.title}`)
      console.log(`   Thumbnail: ${response.data.thumbnail ? 'Available' : 'Not available'}`)
      return true
    } else {
      console.log('❌ Invalid response from video info endpoint')
      console.log('   Response:', response.data)
      return false
    }
  } catch (error) {
    console.log(`❌ Video info endpoint failed: ${error.message}`)
    return false
  }
}

// Test 3: MP3 Endpoint (without downloading)
async function testMp3Endpoint() {
  console.log('\n🔍 Test 3: Testing MP3 endpoint...')
  try {
    const response = await axios.head(`${API_BASE_URL}/mp3/`, {
      params: { url: TEST_VIDEO_URL },
      timeout: API_TIMEOUT,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (response.status === 200) {
      console.log('✅ MP3 endpoint is accessible')
      console.log(`   Content-Type: ${response.headers['content-type'] || 'Unknown'}`)
      return true
    } else {
      console.log(`❌ MP3 endpoint returned status: ${response.status}`)
      return false
    }
  } catch (error) {
    console.log(`❌ MP3 endpoint failed: ${error.message}`)
    return false
  }
}

// Test 4: MP4 Endpoint (without downloading)
async function testMp4Endpoint() {
  console.log('\n🔍 Test 4: Testing MP4 endpoint...')
  try {
    const response = await axios.head(`${API_BASE_URL}/mp4/`, {
      params: { url: TEST_VIDEO_URL },
      timeout: API_TIMEOUT,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })

    if (response.status === 200) {
      console.log('✅ MP4 endpoint is accessible')
      console.log(`   Content-Type: ${response.headers['content-type'] || 'Unknown'}`)
      return true
    } else {
      console.log(`❌ MP4 endpoint returned status: ${response.status}`)
      return false
    }
  } catch (error) {
    console.log(`❌ MP4 endpoint failed: ${error.message}`)
    return false
  }
}

// Test 5: Plugin File Structure
async function testPluginStructure() {
  console.log('\n🔍 Test 5: Checking plugin file structure...')
  try {
    const fs = require('fs')
    const path = require('path')
    
    const pluginPath = path.join(__dirname, 'plugins', 'youtube-api-dl.js')
    
    if (fs.existsSync(pluginPath)) {
      console.log('✅ Plugin file exists')
      
      const content = fs.readFileSync(pluginPath, 'utf8')
      
      // Check for required patterns
      const checks = [
        { pattern: /pattern: 'ytapi \?\(\.\*\)'/, name: 'Main command pattern' },
        { pattern: /handleVideoInfo/, name: 'Video info function' },
        { pattern: /handleVideoDownload/, name: 'Video download function' },
        { pattern: /handleAudioDownload/, name: 'Audio download function' },
        { pattern: /API_BASE_URL/, name: 'API configuration' },
        { pattern: /ytIdRegex/, name: 'YouTube URL regex' }
      ]
      
      let allChecksPass = true
      for (const check of checks) {
        if (check.pattern.test(content)) {
          console.log(`   ✅ ${check.name}`)
        } else {
          console.log(`   ❌ ${check.name}`)
          allChecksPass = false
        }
      }
      
      return allChecksPass
    } else {
      console.log('❌ Plugin file not found')
      return false
    }
  } catch (error) {
    console.log(`❌ Plugin structure check failed: ${error.message}`)
    return false
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting YouTube API Plugin Tests...\n')
  
  const results = {
    apiAvailability: await testApiAvailability(),
    videoInfo: await testVideoInfo(),
    mp3Endpoint: await testMp3Endpoint(),
    mp4Endpoint: await testMp4Endpoint(),
    pluginStructure: await testPluginStructure()
  }
  
  console.log('\n📊 Test Results Summary:')
  console.log('========================')
  
  const passed = Object.values(results).filter(Boolean).length
  const total = Object.keys(results).length
  
  for (const [test, result] of Object.entries(results)) {
    console.log(`${result ? '✅' : '❌'} ${test}: ${result ? 'PASS' : 'FAIL'}`)
  }
  
  console.log(`\n🎯 Overall: ${passed}/${total} tests passed`)
  
  if (passed === total) {
    console.log('🎉 All tests passed! Plugin is ready to use.')
    console.log('\n📝 Next steps:')
    console.log('1. Restart your bot to load the plugin')
    console.log('2. Use .ytapi commands in WhatsApp')
    console.log('3. Test with: .ytapi status')
  } else {
    console.log('⚠️  Some tests failed. Check the issues above.')
    console.log('\n🔧 Troubleshooting:')
    if (!results.apiAvailability) {
      console.log('- API might be down or blocked')
      console.log('- Check your internet connection')
    }
    if (!results.pluginStructure) {
      console.log('- Plugin file might be corrupted')
      console.log('- Re-create the plugin file')
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = {
  testApiAvailability,
  testVideoInfo,
  testMp3Endpoint,
  testMp4Endpoint,
  testPluginStructure,
  runAllTests
}
