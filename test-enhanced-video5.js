/**
 * Test Script for Enhanced Video5 Plugin
 * 
 * Tests the enhanced Video5 plugin with external URL download capabilities
 */

const fs = require('fs')
const path = require('path')

console.log('🧪 Testing Enhanced Video5 Plugin...\n')

// Test 1: Plugin Structure
function testEnhancedStructure() {
  console.log('📁 Test 1: Enhanced Plugin Structure')
  console.log('-----------------------------------')
  
  const pluginPath = path.join(__dirname, 'plugins', 'video5.js')
  
  if (!fs.existsSync(pluginPath)) {
    console.log('❌ Plugin file not found')
    return false
  }
  
  console.log('✅ Plugin file exists')
  
  const content = fs.readFileSync(pluginPath, 'utf8')
  
  const checks = [
    { pattern: /Universal Video Downloader/, name: 'Enhanced title' },
    { pattern: /URL_PATTERNS/, name: 'URL detection patterns' },
    { pattern: /detectUrlType/, name: 'URL type detection function' },
    { pattern: /handleExternalDownload/, name: 'External download handler' },
    { pattern: /youtube:/, name: 'YouTube pattern' },
    { pattern: /tiktok:/, name: 'TikTok pattern' },
    { pattern: /instagram:/, name: 'Instagram pattern' },
    { pattern: /twitter:/, name: 'Twitter pattern' },
    { pattern: /y2mate/, name: 'Y2mate integration' },
    { pattern: /yts/, name: 'YouTube search integration' },
    { pattern: /API\.Video URL import/, name: 'API.Video import method' },
    { pattern: /Download from ANY Platform/, name: 'Enhanced help text' }
  ]
  
  let allChecksPass = true
  for (const check of checks) {
    if (check.pattern.test(content)) {
      console.log(`   ✅ ${check.name}`)
    } else {
      console.log(`   ❌ ${check.name}`)
      allChecksPass = false
    }
  }
  
  console.log(`\n📊 Enhanced Structure Test: ${allChecksPass ? 'PASS' : 'FAIL'}\n`)
  return allChecksPass
}

// Test 2: URL Pattern Detection
function testUrlPatterns() {
  console.log('🔗 Test 2: URL Pattern Detection')
  console.log('--------------------------------')
  
  const testUrls = [
    { url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', expected: 'youtube' },
    { url: 'https://youtu.be/dQw4w9WgXcQ', expected: 'youtube' },
    { url: 'https://tiktok.com/@user/video/1234567890', expected: 'tiktok' },
    { url: 'https://vm.tiktok.com/1234567890', expected: 'tiktok' },
    { url: 'https://instagram.com/p/ABC123DEF456', expected: 'instagram' },
    { url: 'https://twitter.com/user/status/1234567890', expected: 'twitter' },
    { url: 'https://x.com/user/status/1234567890', expected: 'twitter' },
    { url: 'https://facebook.com/user/videos/1234567890', expected: 'facebook' },
    { url: 'https://example.com/video.mp4', expected: 'generic' },
    { url: 'not-a-url', expected: null }
  ]
  
  // Simulate the detectUrlType function
  const URL_PATTERNS = {
    youtube: /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/,
    tiktok: /(?:tiktok\.com\/@[^\/]+\/video\/|vm\.tiktok\.com\/|vt\.tiktok\.com\/)([0-9]+)/,
    instagram: /(?:instagram\.com\/(?:p|reel|tv)\/|instagr\.am\/p\/)([A-Za-z0-9_-]+)/,
    twitter: /(?:twitter\.com|x\.com)\/[^\/]+\/status\/([0-9]+)/,
    facebook: /(?:facebook\.com|fb\.watch)\/.*\/videos?\/([0-9]+)/,
    generic: /^https?:\/\/.+/
  }
  
  const isUrl = (str) => {
    try {
      new URL(str)
      return true
    } catch {
      return false
    }
  }
  
  const detectUrlType = (url) => {
    if (!url || !isUrl(url)) return null
    
    for (const [platform, pattern] of Object.entries(URL_PATTERNS)) {
      if (pattern.test(url)) {
        return platform
      }
    }
    return null
  }
  
  let allTestsPass = true
  for (const test of testUrls) {
    const result = detectUrlType(test.url)
    if (result === test.expected) {
      console.log(`   ✅ ${test.url} -> ${result || 'null'}`)
    } else {
      console.log(`   ❌ ${test.url} -> ${result || 'null'} (expected: ${test.expected || 'null'})`)
      allTestsPass = false
    }
  }
  
  console.log(`\n📊 URL Pattern Test: ${allTestsPass ? 'PASS' : 'FAIL'}\n`)
  return allTestsPass
}

// Test 3: Enhanced Commands
function testEnhancedCommands() {
  console.log('🎮 Test 3: Enhanced Commands')
  console.log('----------------------------')
  
  const pluginPath = path.join(__dirname, 'plugins', 'video5.js')
  const content = fs.readFileSync(pluginPath, 'utf8')
  
  const commands = [
    { pattern: /Download from YouTube/, name: 'YouTube download command' },
    { pattern: /Download from TikTok/, name: 'TikTok download command' },
    { pattern: /Download from Instagram/, name: 'Instagram download command' },
    { pattern: /Download from Twitter/, name: 'Twitter download command' },
    { pattern: /Download from any platform/, name: 'Generic download command' },
    { pattern: /isUrl\(match\)/, name: 'URL detection in handler' },
    { pattern: /handleExternalDownload/, name: 'External download handler call' }
  ]
  
  let allCommandsFound = true
  for (const cmd of commands) {
    if (cmd.pattern.test(content)) {
      console.log(`   ✅ ${cmd.name}`)
    } else {
      console.log(`   ❌ ${cmd.name}`)
      allCommandsFound = false
    }
  }
  
  console.log(`\n📊 Enhanced Commands Test: ${allCommandsFound ? 'PASS' : 'FAIL'}\n`)
  return allCommandsFound
}

// Test 4: Download Methods
function testDownloadMethods() {
  console.log('📥 Test 4: Download Methods')
  console.log('--------------------------')
  
  const pluginPath = path.join(__dirname, 'plugins', 'video5.js')
  const content = fs.readFileSync(pluginPath, 'utf8')
  
  const methods = [
    { pattern: /y2mate\.get/, name: 'Y2mate method' },
    { pattern: /API\.Video URL import/, name: 'API.Video import method' },
    { pattern: /yts\(url/, name: 'YouTube search fallback' },
    { pattern: /source:\s*{\s*type:\s*['"]url['"]/, name: 'URL source configuration' },
    { pattern: /Poll for video readiness/, name: 'Processing polling' },
    { pattern: /Fallback method/, name: 'Fallback mechanisms' }
  ]
  
  let allMethodsFound = true
  for (const method of methods) {
    if (method.pattern.test(content)) {
      console.log(`   ✅ ${method.name}`)
    } else {
      console.log(`   ❌ ${method.name}`)
      allMethodsFound = false
    }
  }
  
  console.log(`\n📊 Download Methods Test: ${allMethodsFound ? 'PASS' : 'FAIL'}\n`)
  return allMethodsFound
}

// Test 5: Error Handling
function testErrorHandling() {
  console.log('🛡️  Test 5: Error Handling')
  console.log('-------------------------')
  
  const pluginPath = path.join(__dirname, 'plugins', 'video5.js')
  const content = fs.readFileSync(pluginPath, 'utf8')
  
  const errorHandling = [
    { pattern: /Y2mate failed, trying API\.Video method/, name: 'Method fallback' },
    { pattern: /API\.Video import failed/, name: 'API import error handling' },
    { pattern: /Fallback method failed/, name: 'Fallback error handling' },
    { pattern: /Video processing timeout/, name: 'Timeout handling' },
    { pattern: /might be private, restricted/, name: 'Access restriction messages' },
    { pattern: /try \{[\s\S]*?\} catch/, name: 'Try-catch blocks' }
  ]
  
  let allErrorHandlingFound = true
  for (const eh of errorHandling) {
    if (eh.pattern.test(content)) {
      console.log(`   ✅ ${eh.name}`)
    } else {
      console.log(`   ❌ ${eh.name}`)
      allErrorHandlingFound = false
    }
  }
  
  console.log(`\n📊 Error Handling Test: ${allErrorHandlingFound ? 'PASS' : 'FAIL'}\n`)
  return allErrorHandlingFound
}

// Run all tests
async function runEnhancedTests() {
  console.log('🚀 Starting Enhanced Video5 Plugin Tests...\n')
  
  const results = {
    enhancedStructure: testEnhancedStructure(),
    urlPatterns: testUrlPatterns(),
    enhancedCommands: testEnhancedCommands(),
    downloadMethods: testDownloadMethods(),
    errorHandling: testErrorHandling()
  }
  
  console.log('🎯 ENHANCED TEST RESULTS')
  console.log('========================')
  
  const passed = Object.values(results).filter(Boolean).length
  const total = Object.keys(results).length
  
  for (const [test, result] of Object.entries(results)) {
    const status = result ? '✅ PASS' : '❌ FAIL'
    console.log(`${status} - ${test}`)
  }
  
  console.log(`\n📊 Overall Score: ${passed}/${total} tests passed`)
  
  if (passed === total) {
    console.log('\n🎉 ALL ENHANCED TESTS PASSED! 🎉')
    console.log('=================================')
    console.log('✅ Plugin supports universal video downloads')
    console.log('✅ Multiple download methods implemented')
    console.log('✅ Comprehensive platform support')
    console.log('✅ Robust error handling and fallbacks')
    
    console.log('\n🌐 SUPPORTED PLATFORMS:')
    console.log('• YouTube (youtube.com, youtu.be)')
    console.log('• TikTok (tiktok.com, vm.tiktok.com)')
    console.log('• Instagram (instagram.com)')
    console.log('• Twitter/X (twitter.com, x.com)')
    console.log('• Facebook (facebook.com)')
    console.log('• Any video URL (generic)')
    
    console.log('\n📝 USAGE EXAMPLES:')
    console.log('.video5 https://youtu.be/dQw4w9WgXcQ')
    console.log('.video5 https://tiktok.com/@user/video/123')
    console.log('.video5 https://instagram.com/p/ABC123')
    console.log('.video5 list')
    console.log('.video5 status')
    
  } else {
    console.log('\n⚠️  SOME ENHANCED TESTS FAILED')
    console.log('==============================')
    console.log('The plugin may still work, but some enhanced features might be missing.')
  }
  
  console.log('\n🔧 NEXT STEPS:')
  console.log('1. Restart your bot: pm2 restart all')
  console.log('2. Test with: .video5 status')
  console.log('3. Try YouTube: .video5 https://youtu.be/dQw4w9WgXcQ')
  console.log('4. Try TikTok: .video5 https://tiktok.com/@user/video/123')
  
  return passed === total
}

// Run tests if this file is executed directly
if (require.main === module) {
  runEnhancedTests().catch(console.error)
}

module.exports = {
  testEnhancedStructure,
  testUrlPatterns,
  testEnhancedCommands,
  testDownloadMethods,
  testErrorHandling,
  runEnhancedTests
}
