/**
 * Test Script for Video5 Plugin
 * 
 * Tests the API.Video connection and plugin functionality
 */

const axios = require('axios')

// Configuration
const API_KEY = 'GndDicHo3f3te7KL2J44TfUlVMkLXhXuPILz424WJt0'
const API_BASE_URL = 'https://ws.api.video'

console.log('🧪 Testing Video5 Plugin...\n')

// Test 1: Plugin File Structure
function testPluginStructure() {
  console.log('📁 Test 1: Plugin File Structure')
  console.log('--------------------------------')
  
  const fs = require('fs')
  const path = require('path')
  
  const pluginPath = path.join(__dirname, 'plugins', 'video5.js')
  
  if (!fs.existsSync(pluginPath)) {
    console.log('❌ Plugin file not found')
    return false
  }
  
  console.log('✅ Plugin file exists')
  
  const content = fs.readFileSync(pluginPath, 'utf8')
  
  const checks = [
    { pattern: /pattern: 'video5 \?\(\.\*\)'/, name: 'Main command pattern' },
    { pattern: /handleListVideos/, name: 'List videos function' },
    { pattern: /handleSearchVideos/, name: 'Search videos function' },
    { pattern: /handleVideoInfo/, name: 'Video info function' },
    { pattern: /handleVideoDownload/, name: 'Video download function' },
    { pattern: /handleApiStatus/, name: 'API status function' },
    { pattern: /API_KEY/, name: 'API key configuration' },
    { pattern: /API_BASE_URL/, name: 'API base URL configuration' }
  ]
  
  let allChecksPass = true
  for (const check of checks) {
    if (check.pattern.test(content)) {
      console.log(`   ✅ ${check.name}`)
    } else {
      console.log(`   ❌ ${check.name}`)
      allChecksPass = false
    }
  }
  
  console.log(`\n📊 Structure Test: ${allChecksPass ? 'PASS' : 'FAIL'}\n`)
  return allChecksPass
}

// Test 2: API Connection
async function testApiConnection() {
  console.log('🔗 Test 2: API Connection')
  console.log('-------------------------')
  
  try {
    const startTime = Date.now()
    const response = await axios.get(`${API_BASE_URL}/videos`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      params: {
        pageSize: 1
      },
      timeout: 10000
    })
    
    const responseTime = Date.now() - startTime
    
    if (response.status === 200) {
      console.log('✅ API connection successful')
      console.log(`   Response time: ${responseTime}ms`)
      console.log(`   Total videos: ${response.data.pagination?.itemsTotal || 0}`)
      console.log('\n📊 API Connection Test: PASS\n')
      return true
    } else {
      console.log(`❌ Unexpected status: ${response.status}`)
      console.log('\n📊 API Connection Test: FAIL\n')
      return false
    }
    
  } catch (error) {
    console.log('❌ API connection failed')
    
    if (error.response?.status === 401) {
      console.log('   Issue: Invalid API key')
    } else if (error.response?.status === 403) {
      console.log('   Issue: Access forbidden')
    } else if (error.code === 'ENOTFOUND') {
      console.log('   Issue: Network connection failed')
    } else {
      console.log(`   Error: ${error.message}`)
    }
    
    console.log('\n📊 API Connection Test: FAIL\n')
    return false
  }
}

// Test 3: Video Listing
async function testVideoListing() {
  console.log('📋 Test 3: Video Listing')
  console.log('------------------------')
  
  try {
    const response = await axios.get(`${API_BASE_URL}/videos`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      params: {
        pageSize: 5,
        sortBy: 'publishedAt',
        sortOrder: 'desc'
      },
      timeout: 10000
    })
    
    if (response.data && response.data.data) {
      const videos = response.data.data
      console.log(`✅ Successfully retrieved ${videos.length} videos`)
      
      if (videos.length > 0) {
        console.log('   Sample video:')
        const video = videos[0]
        console.log(`   - Title: ${video.title}`)
        console.log(`   - ID: ${video.videoId}`)
        console.log(`   - Published: ${new Date(video.publishedAt).toLocaleDateString()}`)
        console.log(`   - MP4 Available: ${video.assets?.mp4 ? 'Yes' : 'No'}`)
      }
      
      console.log('\n📊 Video Listing Test: PASS\n')
      return true
    } else {
      console.log('❌ Invalid response format')
      console.log('\n📊 Video Listing Test: FAIL\n')
      return false
    }
    
  } catch (error) {
    console.log('❌ Video listing failed')
    console.log(`   Error: ${error.message}`)
    console.log('\n📊 Video Listing Test: FAIL\n')
    return false
  }
}

// Test 4: Search Functionality
async function testSearchFunctionality() {
  console.log('🔍 Test 4: Search Functionality')
  console.log('-------------------------------')
  
  try {
    const searchTerm = 'video' // Generic search term
    const response = await axios.get(`${API_BASE_URL}/videos`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      params: {
        title: searchTerm,
        pageSize: 3
      },
      timeout: 10000
    })
    
    console.log(`✅ Search for "${searchTerm}" completed`)
    console.log(`   Results found: ${response.data.data?.length || 0}`)
    
    console.log('\n📊 Search Functionality Test: PASS\n')
    return true
    
  } catch (error) {
    console.log('❌ Search functionality failed')
    console.log(`   Error: ${error.message}`)
    console.log('\n📊 Search Functionality Test: FAIL\n')
    return false
  }
}

// Test 5: Plugin Commands
function testPluginCommands() {
  console.log('🎮 Test 5: Plugin Commands')
  console.log('--------------------------')
  
  const fs = require('fs')
  const path = require('path')
  
  const pluginPath = path.join(__dirname, 'plugins', 'video5.js')
  const content = fs.readFileSync(pluginPath, 'utf8')
  
  const commands = [
    { pattern: /video5 list/, name: 'List command' },
    { pattern: /video5 search/, name: 'Search command' },
    { pattern: /video5 info/, name: 'Info command' },
    { pattern: /video5 download/, name: 'Download command' },
    { pattern: /video5 status/, name: 'Status command' }
  ]
  
  let allCommandsFound = true
  for (const cmd of commands) {
    if (cmd.pattern.test(content)) {
      console.log(`   ✅ ${cmd.name}`)
    } else {
      console.log(`   ❌ ${cmd.name}`)
      allCommandsFound = false
    }
  }
  
  console.log(`\n📊 Plugin Commands Test: ${allCommandsFound ? 'PASS' : 'FAIL'}\n`)
  return allCommandsFound
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Video5 Plugin Tests...\n')
  
  const results = {
    structure: testPluginStructure(),
    apiConnection: await testApiConnection(),
    videoListing: await testVideoListing(),
    searchFunctionality: await testSearchFunctionality(),
    pluginCommands: testPluginCommands()
  }
  
  console.log('🎯 FINAL TEST RESULTS')
  console.log('=====================')
  
  const passed = Object.values(results).filter(Boolean).length
  const total = Object.keys(results).length
  
  for (const [test, result] of Object.entries(results)) {
    const status = result ? '✅ PASS' : '❌ FAIL'
    console.log(`${status} - ${test}`)
  }
  
  console.log(`\n📊 Overall Score: ${passed}/${total} tests passed`)
  
  if (passed === total) {
    console.log('\n🎉 ALL TESTS PASSED! 🎉')
    console.log('========================')
    console.log('✅ Plugin is ready for production use')
    console.log('✅ API connection is working')
    console.log('✅ All features are functional')
    
    console.log('\n📝 NEXT STEPS:')
    console.log('1. Restart your bot: pm2 restart all')
    console.log('2. Test in WhatsApp: .video5 status')
    console.log('3. List videos: .video5 list')
    console.log('4. Download a video: .video5 download <videoId>')
    
  } else {
    console.log('\n⚠️  SOME TESTS FAILED')
    console.log('====================')
    
    if (!results.apiConnection) {
      console.log('🚨 CRITICAL: API connection failed!')
      console.log('   - Check your API key')
      console.log('   - Verify internet connection')
      console.log('   - Check api.video service status')
    }
    
    if (!results.structure) {
      console.log('🚨 CRITICAL: Plugin structure issues!')
      console.log('   - Check plugin file integrity')
      console.log('   - Verify all functions are present')
    }
  }
  
  console.log('\n🔧 PLUGIN USAGE:')
  console.log('- .video5 list - List all your videos')
  console.log('- .video5 search <term> - Search videos')
  console.log('- .video5 info <id> - Get video details')
  console.log('- .video5 download <id> - Download video')
  console.log('- .video5 status - Check API status')
  
  return passed === total
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = {
  testPluginStructure,
  testApiConnection,
  testVideoListing,
  testSearchFunctionality,
  testPluginCommands,
  runAllTests
}
